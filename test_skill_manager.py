#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技能管理器测试和分析脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_skill_manager_imports():
    """测试技能管理器导入"""
    print("=== 测试技能管理器导入 ===")
    
    try:
        from core.skill_manager import SkillManager, SkillSlot
        print("✓ SkillManager和SkillSlot导入成功")
        return True
    except Exception as e:
        print(f"✗ 技能管理器导入失败: {e}")
        return False

def test_skill_slot_functionality():
    """测试技能槽位功能"""
    print("=== 测试技能槽位功能 ===")
    
    try:
        from core.skill_manager import SkillSlot
        
        # 创建技能槽位
        slot = SkillSlot(1, "fireball", True, False)
        
        # 测试基本属性
        assert slot.slot_id == 1, "槽位ID应该为1"
        assert slot.skill_id == "fireball", "技能ID应该为fireball"
        assert slot.enabled == True, "技能应该启用"
        assert slot.auto_cast == False, "自动释放应该关闭"
        
        # 测试方法
        assert not slot.is_initial_slot(), "1号槽位不应该是初始槽位"
        assert not slot.is_empty(), "槽位不应该为空"
        
        # 测试切换功能
        enabled = slot.toggle_enabled()
        assert enabled == False, "切换后应该禁用"
        
        auto_cast = slot.toggle_auto_cast()
        assert auto_cast == True, "切换后应该自动释放"
        
        print("✓ 技能槽位功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 技能槽位功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_skill_manager_initialization():
    """测试技能管理器初始化"""
    print("=== 测试技能管理器初始化 ===")
    
    try:
        from core.skill_manager import SkillManager
        from core.player import Player
        
        # 创建玩家
        player = Player("法师", "测试法师", "男")
        
        # 创建技能管理器
        skill_manager = SkillManager(player)
        
        # 检查槽位创建
        assert len(skill_manager.skill_slots) == 7, "应该有7个技能槽位(0-6)"
        assert 0 in skill_manager.skill_slots, "应该有0号初始技能槽"
        
        # 检查初始技能设置
        initial_slot = skill_manager.skill_slots[0]
        assert initial_slot.skill_id == "fireball", "法师初始技能应该是火球术"
        
        print("✓ 技能管理器初始化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 技能管理器初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_skill_configuration():
    """测试技能配置"""
    print("=== 测试技能配置 ===")
    
    try:
        import json
        
        # 加载技能配置
        with open('data/configs/skills.json', 'r', encoding='utf-8') as f:
            skills_data = json.load(f)
        
        # 检查配置结构
        assert "warrior" in skills_data, "应该有战士技能配置"
        assert "mage" in skills_data, "应该有法师技能配置"
        assert "taoist" in skills_data, "应该有道士技能配置"
        
        # 检查技能数量
        warrior_skills = len(skills_data["warrior"])
        mage_skills = len(skills_data["mage"])
        taoist_skills = len(skills_data["taoist"])
        
        print(f"✓ 技能配置加载成功: 战士{warrior_skills}个, 法师{mage_skills}个, 道士{taoist_skills}个")
        
        # 检查必要技能
        assert "fireball" in skills_data["mage"], "法师应该有火球术"
        assert "healing" in skills_data["taoist"], "道士应该有治愈术"
        
        print("✓ 技能配置测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 技能配置测试失败: {e}")
        return False

def analyze_placeholder_methods():
    """分析占位符方法"""
    print("=== 分析占位符方法 ===")
    
    try:
        with open('core/skill_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找占位符方法
        import re
        placeholder_pattern = r'def\s+(_apply_\w+_skill)\(.*?\):\s*""".*?"""\s*logger\.info\(".*?占位符.*?"\)'
        placeholders = re.findall(placeholder_pattern, content, re.DOTALL)
        
        print(f"发现 {len(placeholders)} 个占位符方法:")
        for method in placeholders:
            print(f"  - {method}")
        
        # 分析影响
        if len(placeholders) > 0:
            print(f"⚠️  {len(placeholders)}个技能效果未实现，影响游戏功能完整性")
        else:
            print("✓ 所有技能效果都已实现")
        
        return True
        
    except Exception as e:
        print(f"✗ 占位符方法分析失败: {e}")
        return False

def analyze_method_complexity():
    """分析方法复杂度"""
    print("=== 分析方法复杂度 ===")
    
    try:
        import ast
        
        with open('core/skill_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        long_methods = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if hasattr(node, 'end_lineno'):
                    method_lines = node.end_lineno - node.lineno + 1
                    if method_lines > 50:
                        long_methods.append((node.name, method_lines))
        
        if long_methods:
            print("发现过长的方法:")
            for name, lines in long_methods:
                if lines > 100:
                    print(f"  ❌ {name}: {lines}行 (严重)")
                elif lines > 80:
                    print(f"  ⚠️  {name}: {lines}行 (需要重构)")
                else:
                    print(f"  🟡 {name}: {lines}行 (较长)")
        else:
            print("✓ 所有方法长度合理")
        
        return True
        
    except Exception as e:
        print(f"✗ 方法复杂度分析失败: {e}")
        return False

def test_skill_effects():
    """测试技能效果"""
    print("=== 测试技能效果 ===")
    
    try:
        from core.skill_manager import SkillManager
        from core.player import Player
        
        # 创建测试环境
        player = Player("法师", "测试法师", "男")
        skill_manager = SkillManager(player)
        
        # 模拟游戏管理器
        class MockGameManager:
            def __init__(self):
                self.player = player
                self.in_battle = True
                self.current_enemy = None
                self.logs = []
            
            def add_log(self, message):
                self.logs.append(message)
        
        game_manager = MockGameManager()
        
        # 测试技能效果应用
        skill_result = {
            "skill_id": "fireball",
            "skill_name": "火球术",
            "effect_type": "direct_damage",
            "effect_value": 50
        }
        
        # 这应该不会抛出异常
        skill_manager._apply_skill_effect(game_manager, skill_result)
        
        print("✓ 技能效果系统基本功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 技能效果测试失败: {e}")
        return False

def generate_improvement_suggestions():
    """生成改进建议"""
    print("=== 改进建议 ===")
    
    suggestions = [
        "🔴 高优先级:",
        "  1. 重构auto_cast_skills方法 - 拆分为多个小方法",
        "  2. 实现14个占位符技能效果方法",
        "  3. 添加技能配置缓存机制",
        "",
        "🟡 中优先级:",
        "  1. 统一初始技能处理逻辑",
        "  2. 优化数据同步机制",
        "  3. 完善技能冷却UI显示",
        "",
        "🟢 低优先级:",
        "  1. 优化槽位遍历性能",
        "  2. 添加技能组合系统",
        "  3. 实现技能音效系统"
    ]
    
    for suggestion in suggestions:
        print(suggestion)

def main():
    """主测试函数"""
    print("开始技能管理器测试和分析...\n")
    
    tests = [
        test_skill_manager_imports,
        test_skill_slot_functionality,
        test_skill_manager_initialization,
        test_skill_configuration,
        analyze_placeholder_methods,
        analyze_method_complexity,
        test_skill_effects
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 发生异常: {e}")
            print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！技能管理器基本功能正常！")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过，技能管理器基本可用")
    else:
        print("❌ 多个测试失败，技能管理器需要修复")
    
    print()
    generate_improvement_suggestions()

if __name__ == "__main__":
    main()
