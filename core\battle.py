import random
import time
from typing import Dict, List, Tuple, Any, Optional, Union, Callable, TYPE_CHECKING
from utils.logger import logger
from core.config import GameConfig

# 类型提示导入
if TYPE_CHECKING:
    from core.player import Player
    from core.monster import Monster


class BattleStateManager:
    """战斗状态管理器 - 统一管理战斗状态和结束条件"""

    def __init__(self):
        self.battle_ended = False
        self.winner = None
        self.end_reason = None

    def reset(self):
        """重置战斗状态"""
        self.battle_ended = False
        self.winner = None
        self.end_reason = None

    def check_battle_end_conditions(self, player, monster):
        """统一检查战斗结束条件

        返回:
            bool: 是否应该结束战斗
        """
        if not player:
            self.battle_ended = True
            self.winner = "monster"
            self.end_reason = "player_missing"
            return True

        if player.hp <= 0:
            self.battle_ended = True
            self.winner = "monster"
            self.end_reason = "player_death"
            return True

        if not monster:
            self.battle_ended = True
            self.winner = "player"
            self.end_reason = "monster_missing"
            return True

        if monster.hp <= 0:
            self.battle_ended = True
            self.winner = "player"
            self.end_reason = "monster_death"
            return True

        return False


class DamageCalculator:
    """统一的伤害计算系统"""

    @staticmethod
    def calculate_hit_rate(attacker_accuracy, target_agility):
        """统一的命中率计算

        参数:
            attacker_accuracy: 攻击者准确度
            target_agility: 目标敏捷度

        返回:
            float: 命中率 (0.0-1.0)
        """
        # 基础命中率90%，根据准确度和敏捷度调整
        base_hit_rate = 0.9
        accuracy_factor = (attacker_accuracy - target_agility) * 0.01
        hit_rate = base_hit_rate + accuracy_factor

        # 限制在30%-95%之间
        return max(0.3, min(0.95, hit_rate))

    @staticmethod
    def calculate_damage(attacker, target, damage_type="physical"):
        """统一的伤害计算方法

        参数:
            attacker: 攻击者对象
            target: 目标对象
            damage_type: 伤害类型 ("physical", "magic", "taoism")

        返回:
            tuple: (最终伤害, 是否暴击)
        """
        try:
            # 获取基础伤害
            base_damage, is_crit = DamageCalculator._get_base_damage(attacker, damage_type)

            # 应用防御减伤
            final_damage = DamageCalculator._apply_defense(base_damage, target, damage_type)

            return max(1, final_damage), is_crit

        except Exception as e:
            logger.error(f"计算伤害时出错: {e}")
            return 1, False

    @staticmethod
    def _get_base_damage(attacker, damage_type):
        """获取基础伤害"""
        if hasattr(attacker, 'calculate_damage') and damage_type == "physical":
            return attacker.calculate_damage()
        elif hasattr(attacker, 'calculate_magic_damage') and damage_type == "magic":
            return attacker.calculate_magic_damage()
        elif hasattr(attacker, 'calculate_taoism_damage') and damage_type == "taoism":
            return attacker.calculate_taoism_damage()
        elif hasattr(attacker, 'attack_range'):
            # 怪物或召唤物的默认伤害计算
            min_dmg = attacker.attack_range[0] if isinstance(attacker.attack_range, (list, tuple)) else attacker.attack_range.get('min', 1)
            max_dmg = attacker.attack_range[1] if isinstance(attacker.attack_range, (list, tuple)) else attacker.attack_range.get('max', 2)
            damage = random.randint(int(min_dmg), int(max_dmg))

            # 检查暴击
            crit_rate = getattr(attacker, 'crit_rate', 0.05)
            is_crit = random.random() < crit_rate
            if is_crit:
                damage = int(damage * 1.5)

            return damage, is_crit
        else:
            return 1, False

    @staticmethod
    def _apply_defense(damage, target, damage_type):
        """应用防御减伤"""
        if damage_type == "physical":
            defense = getattr(target, 'defense', 0)
        elif damage_type in ["magic", "taoism"]:
            defense = getattr(target, 'magic_defense', 0)
        else:
            defense = getattr(target, 'defense', 0)

        return max(1, damage - defense)


class CooldownManager:
    """冷却管理器 - 统一管理技能和攻击冷却"""

    def __init__(self):
        self.global_cooldown = 0
        self.skill_cooldowns = {}
        self.attack_cooldowns = {}

    def can_use_skill(self, skill_id, current_time):
        """检查技能是否可以使用

        参数:
            skill_id: 技能ID
            current_time: 当前时间

        返回:
            bool: 是否可以使用技能
        """
        # 检查全局冷却
        if current_time < self.global_cooldown:
            return False

        # 检查技能冷却
        if skill_id in self.skill_cooldowns:
            return current_time >= self.skill_cooldowns[skill_id]

        return True

    def can_attack(self, attacker_id, current_time, attack_interval):
        """检查是否可以攻击

        参数:
            attacker_id: 攻击者ID
            current_time: 当前时间
            attack_interval: 攻击间隔

        返回:
            bool: 是否可以攻击
        """
        if attacker_id not in self.attack_cooldowns:
            return True

        return current_time - self.attack_cooldowns[attacker_id] >= attack_interval

    def set_skill_cooldown(self, skill_id, cooldown_time, current_time):
        """设置技能冷却

        参数:
            skill_id: 技能ID
            cooldown_time: 冷却时间
            current_time: 当前时间
        """
        self.skill_cooldowns[skill_id] = current_time + cooldown_time

    def set_global_cooldown(self, cooldown_time, current_time):
        """设置全局冷却

        参数:
            cooldown_time: 冷却时间
            current_time: 当前时间
        """
        self.global_cooldown = current_time + cooldown_time

    def set_attack_cooldown(self, attacker_id, current_time):
        """设置攻击冷却

        参数:
            attacker_id: 攻击者ID
            current_time: 当前时间
        """
        self.attack_cooldowns[attacker_id] = current_time

    def update_cooldowns(self, current_time):
        """更新冷却状态

        参数:
            current_time: 当前时间
        """
        # 清理过期的技能冷却
        expired_skills = [skill_id for skill_id, end_time in self.skill_cooldowns.items()
                         if current_time >= end_time]
        for skill_id in expired_skills:
            del self.skill_cooldowns[skill_id]

        # 更新全局冷却
        if current_time >= self.global_cooldown:
            self.global_cooldown = 0

class BattleSystem:
    """战斗系统，管理战斗逻辑"""

    def __init__(self):
        """初始化战斗系统"""

        self.player: Optional['Player'] = None
        self.monster: Optional['Monster'] = None
        self.battle_active: bool = False
        self.auto_battle: bool = False
        self.battle_start_time: float = 0
        self.last_update_time: float = 0
        self.player_attack_timer: float = 0
        self.monster_attack_timer: float = 0
        self.callbacks: Dict[str, List[Callable]] = {}  # 初始化回调字典
        self.monster_death_callback: Optional[Callable] = None
        self.player_death_callback: Optional[Callable] = None
        self.battle_logs: List[str] = []  # 初始化战斗日志列表
        self.max_log_entries: int = 20 # 初始化日志最大条目数

        # 战斗状态管理器
        self.state_manager = BattleStateManager()

        # 战斗统计
        self.reset_battle_stats()

        # 战斗计时器
        self.battle_start_time = 0
        self.last_player_attack = 0
        self.last_monster_attack = 0
        self._last_attack_time = 0  # 上次执行战斗回合的时间

        # 战斗锁，防止同一帧内多次处理战斗
        self._processing_battle = False

    def reset_battle_stats(self):
        """重置战斗统计数据"""
        self.player_damage_dealt = 0    # 玩家造成的总伤害
        self.player_attacks = 0          # 玩家攻击次数
        self.player_crits = 0           # 玩家暴击次数
        self.monster_damage_dealt = 0    # 怪物造成的总伤害
        self.monster_attacks = 0         # 怪物攻击次数

        # 添加新的统计项
        self.summon_damage_dealt = 0     # 召唤物造成的总伤害
        self.summon_attacks = 0          # 召唤物攻击次数
        self.exp_gained = 0              # 获得的经验值
        self.gold_gained = 0             # 获得的金币
        self.equipment_dropped = 0       # 掉落的装备数量
        self.poison_damage = 0           # 毒药造成的伤害
        self.ground_effect_damage = 0    # 地面效果造成的伤害

    def register_callback(self, event_type: str, callback: Callable):
        """注册回调函数"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
        else:
            logger.warning(f"未知的事件类型: {event_type}")

    def set_monster_death_callback(self, callback: Callable):
        """设置怪物死亡回调函数"""
        logger.info("设置怪物死亡回调函数")
        self.callbacks["on_monster_death"] = [callback]

    def set_player_death_callback(self, callback: Callable):
        """设置玩家死亡回调函数"""
        logger.info("设置玩家死亡回调函数")
        self.callbacks["on_player_death"] = [callback]

    def start_battle(self, player: 'Player', monster: 'Monster') -> bool:
        """
        开始战斗

        参数:
            player: 玩家对象
            monster: 怪物对象

        返回:
            bool: 是否成功开始战斗
        """
        try:
            if not player or not monster:
                logger.error("无法开始战斗: 玩家或怪物对象为空")
                return False

            # 重置战斗状态管理器
            self.state_manager.reset()

            # 设置战斗对象
            self.player = player
            self.monster = monster

            # --- 新增：重置怪物死亡处理标记 ---
            if hasattr(self.monster, 'death_handled'):
                self.monster.death_handled = False
            else:
                # 如果Monster对象没有这个属性（预防性措施），添加它
                setattr(self.monster, 'death_handled', False)
            # --- 结束新增 ---

            # 激活战斗状态
            self.battle_active = True
            # 不重置自动战斗状态，保持与游戏主类同步
            self.battle_logs = []
            self.battle_start_time = time.time()
            self.last_player_attack = time.time()
            self.last_monster_attack = time.time()
            self._last_attack_time = 0

            # 重置召唤物的攻击冷却 - 让召唤物立即可以攻击
            if hasattr(player, 'summons') and player.summons:
                current_time = time.time()
                for summon in player.summons:
                    if hasattr(summon, 'last_attack_time') and hasattr(summon, 'attack_interval'):
                        # 设置一个略早的时间，确保冷却已过
                        summon.last_attack_time = current_time - summon.attack_interval - 0.1
                        logger.debug(f"重置召唤物 {summon.name} 的攻击冷却时间，使其可以立即攻击")

            # 记录战斗开始日志 - 使用醒目格式
            self.add_log(f"战斗开始: {player.character_class} vs {monster.name}")
            self.add_log(f"{monster.name} (Lv.{monster.level}) HP:{monster.hp}")

            # 触发战斗开始回调
            for callback in self.callbacks.get("on_battle_start", []):
                try:
                    callback(player, monster)
                except Exception as e:
                    logger.error(f"战斗开始回调执行失败: {e}")

            return True

        except Exception as e:
            logger.error(f"开始战斗时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def end_battle(self, player_victory: bool = False):
        """
        结束战斗

        参数:
            player_victory: 玩家是否获胜
        """
        if not self.battle_active:
            return

        # 记录战斗结束
        if player_victory:
            self.add_log("战斗胜利!")
        else:
            self.add_log("战斗失败!")

        # 清除怪物的所有效果
        if self.monster and hasattr(self.monster, "reset_effects"):
            self.monster.reset_effects()

        # 执行回调
        for callback in self.callbacks.get("on_battle_end", []):
            try:
                callback(player_victory)
            except Exception as e:
                logger.error(f"战斗结束回调执行失败: {e}")

        # 重置战斗状态
        self.battle_active = False

        # 清除玩家和怪物的引用，避免内存泄漏
        self.player = None
        self.monster = None

        # 确保释放战斗锁
        self._processing_battle = False

        logger.info(f"战斗系统：战斗结束, 玩家{'胜利' if player_victory else '失败'}")

    def update(self, current_time: float):
        """
        更新战斗状态

        参数:
            current_time: 当前时间
        """
        try:
            # 防止重复处理战斗
            if self._processing_battle:
                return

            self._processing_battle = True

            if not self.battle_active:
                self._processing_battle = False
                return

            # 使用状态管理器检查战斗结束条件
            if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                logger.info(f"战斗结束: {self.state_manager.end_reason}, 胜利者: {self.state_manager.winner}")

                if self.state_manager.winner == "player":
                    self.handle_monster_death()
                elif self.state_manager.winner == "monster":
                    self.handle_player_death()
                else:
                    self.end_battle(False)

                self._processing_battle = False
                return

            # 检查对象完整性
            if not hasattr(self.player, 'hp') or not hasattr(self.monster, 'hp'):
                logger.error("战斗对象缺少必要属性，结束战斗")
                self.end_battle(False)
                self._processing_battle = False
                return

            if self.monster.hp <= 0:
                logger.info("战斗系统更新：怪物HP为0，处理怪物死亡")
                self.handle_monster_death()
                self._processing_battle = False
                return

            # 处理地面效果
            self._update_ground_effects(current_time)

            # 检查地面效果处理后怪物是否死亡
            if self.monster.hp <= 0:
                logger.info("战斗系统更新：怪物被地面效果杀死，处理怪物死亡")
                self.handle_monster_death()
                self._processing_battle = False
                return

            # 处理毒药效果
            self._update_poison_effects(current_time)

            # 检查处理毒药效果后怪物是否死亡
            if self.monster.hp <= 0:
                logger.info("战斗系统更新：怪物因中毒死亡，处理怪物死亡")
                self.handle_monster_death()
                self._processing_battle = False
                return

            # 总是更新召唤物战斗状态
            self.update_summons()

            # 检查怪物是否在召唤物攻击后死亡
            if self.monster.hp <= 0:
                logger.info("战斗系统更新：怪物被召唤物杀死，处理怪物死亡")
                self.handle_monster_death()
                self._processing_battle = False
                return

            # 自动战斗逻辑
            if self.auto_battle:
                logger.debug(f"战斗系统更新：自动战斗状态={self.auto_battle}")

                if not self._last_attack_time:
                    logger.info("战斗系统更新：自动战斗首次攻击")
                    self._last_attack_time = current_time
                    # 初始自动战斗时立即执行一轮攻击
                    self.execute_battle_round()
                else:
                    # 计算攻击间隔
                    elapsed = current_time - self._last_attack_time
                    # 基于玩家的攻击速度决定攻击间隔，确保不会出现除零错误
                    attack_speed = max(0.1, getattr(self.player, 'attack_speed', 1.0))

                    # 应用全局战斗速度修正
                    battle_speed_modifier = getattr(GameConfig, 'BATTLE_SPEED_MODIFIER', 1.0)
                    modified_speed = attack_speed * battle_speed_modifier

                    # 计算修正后的攻击间隔，确保不会太短
                    attack_interval = max(0.5, 1.0 / max(0.1, modified_speed))

                    if elapsed >= attack_interval:
                        logger.debug(f"战斗系统更新：自动战斗时间间隔已到({elapsed:.2f}秒 >= {attack_interval:.2f}秒)，执行战斗回合")
                        self._last_attack_time = current_time
                        self.execute_battle_round()

            # 释放战斗锁
            self._processing_battle = False

        except Exception as e:
            logger.error(f"战斗系统更新时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 发生异常时结束战斗
            self.end_battle(False)
            # 确保释放战斗锁
            self._processing_battle = False

    def execute_battle_round(self):
        """执行一个完整的战斗回合"""
        try:
            current_time = time.time()

            # 使用状态管理器检查战斗结束条件
            if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                self._processing_battle = False
                return

            # 1. 自动释放技能 (如果启用)
            if hasattr(self.player, 'skill_manager') and self.player.skill_manager:
                self.player.skill_manager.auto_cast_skills(self)

            # 检查战斗状态（技能可能杀死怪物）
            if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                self._processing_battle = False
                return

            # 2. 更新效果 (如毒药、地面效果)
            self._update_poison_effects(current_time)
            if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                self._processing_battle = False
                return

            self._update_ground_effects(current_time)
            if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                self._processing_battle = False
                return

            # 3. 玩家行动 (如果冷却完成)
            player_attack_interval = self.player.attack_interval
            if current_time - self.last_player_attack >= player_attack_interval:
                self.player_attack()
                self.last_player_attack = current_time

                # 检查战斗状态
                if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                    self._processing_battle = False
                    return

            # 4. 怪物行动 (如果冷却完成且存活)
            monster_attack_interval = self.monster.attack_interval
            if current_time - self.last_monster_attack >= monster_attack_interval:
                self.monster_attack()
                self.last_monster_attack = current_time

                # 检查战斗状态
                if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                    self._processing_battle = False
                    return

            # 5. 召唤物行动 (如果存在且冷却完成)
            self.update_summons()

            # 检查战斗状态
            if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                self._processing_battle = False
                return

            # 6. 魅惑怪物行动 (如果存在且冷却完成)
            self.update_charmed_monsters()

            # 最终检查战斗状态
            if self.state_manager.check_battle_end_conditions(self.player, self.monster):
                self._processing_battle = False
                return

        except AttributeError as ae:
             # 捕捉可能的 NoneType 错误，提供更详细的日志
             import traceback
             tb = traceback.format_exc()
             logger.error(f"AttributeError in execute_battle_round: {ae}")
             logger.error(f"Player: {self.player}, Monster: {self.monster}")
             logger.error(f"Traceback:\\n{tb}")
             # 尝试安全地结束战斗，防止无限循环
             if self.battle_active:
                 logger.error("Forcing battle end due to AttributeError.")
                 # 不确定谁是胜利者，可能需要根据具体错误判断，或者默认玩家失败？
                 self.end_battle(player_victory=False)
        except Exception as e:
            logger.error(f"执行战斗回合时发生未预料的错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
             # 确保在任何情况下都释放处理锁
             self._processing_battle = False

    def player_attack(self):
        """处理玩家攻击"""
        # 如果玩家正在施放技能（法师/道士），则跳过本次普攻
        if self.player and self.player.is_casting_skill:
            logger.debug("玩家攻击被跳过，因为正在施放技能")
            return

        # 检查攻击间隔
        current_time = time.time()
        if current_time - self.last_player_attack < self.player.attack_interval:
            return

        if not self.battle_active or not self.player or not self.monster:
            return

        try:
            # 检查命中率
            player_accuracy = getattr(self.player, 'base_accuracy', 100)
            monster_agility = getattr(self.monster, 'agility', 10)
            hit_rate = DamageCalculator.calculate_hit_rate(player_accuracy, monster_agility)

            if random.random() > hit_rate:
                # 攻击未命中
                self.add_log(f"{self.player.character_class} 的攻击被 {self.monster.name} 闪避了！")
                self.player_attacks += 1  # 记录攻击次数，即使未命中
                return

            # 使用统一的伤害计算系统
            final_damage, is_crit = DamageCalculator.calculate_damage(self.player, self.monster, "physical")

            # 记录日志
            if is_crit:
                self.add_log(f"{self.player.character_class} 暴击 {self.monster.name} 造成 {final_damage} 点伤害!")
            else:
                self.add_log(f"{self.player.character_class} 攻击 {self.monster.name} 造成 {final_damage} 点伤害")

            # 应用伤害并更新统计
            self.monster.hp = max(0, self.monster.hp - final_damage)
            self.player_damage_dealt += final_damage
            self.player_attacks += 1
            if is_crit:
                self.player_crits += 1

            # 处理吸血效果
            self._handle_lifesteal(final_damage)

            # 更新玩家上次攻击时间戳
            self.last_player_attack = current_time

            # 触发伤害回调
            for callback in self.callbacks.get("on_damage_dealt", []):
                try:
                    callback(self.player, self.monster, final_damage, is_crit)
                except Exception as e:
                    logger.error(f"伤害回调执行失败: {e}")

            # 显示怪物剩余血量（如果还活着）
            if self.monster.hp > 0:
                self.add_log(f"{self.monster.name} 剩余生命值: {self.monster.hp}/{self.monster.max_hp}")

        except Exception as e:
            logger.error(f"玩家攻击时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _handle_lifesteal(self, damage_dealt: int):
        """处理玩家攻击吸血效果"""
        try:
            # 获取装备吸血加成
            equipment_bonus = self.player.get_equipment_bonus()

            # 获取百分比吸血率
            lifesteal_rate = equipment_bonus.get("lifesteal", 0)

            # 获取固定数值吸血
            flat_lifesteal = equipment_bonus.get("flat_lifesteal", 0)

            # 计算总吸血量
            heal_amount = 0

            # 处理百分比吸血
            if lifesteal_rate > 0:
                percent_heal = int(damage_dealt * (lifesteal_rate / 100))
                heal_amount += percent_heal

            # 添加固定值吸血
            heal_amount += flat_lifesteal

            # 执行生命恢复
            if heal_amount > 0:
                original_hp = self.player.hp
                self.player.hp = min(self.player.max_hp, self.player.hp + heal_amount)
                actual_heal = self.player.hp - original_hp

                # 添加吸血日志
                if actual_heal > 0:
                    log_detail = ""
                    if lifesteal_rate > 0 and flat_lifesteal > 0:
                        log_detail = "(比例+固定)"
                    elif lifesteal_rate > 0:
                        log_detail = "(比例)"
                    elif flat_lifesteal > 0:
                        log_detail = "(固定)"

                    self.add_log(f"装备吸血效果恢复 {actual_heal} 点生命值 {log_detail}")
                    logger.debug(f"吸血效果: 伤害={damage_dealt}, 吸血率={lifesteal_rate}%, 固定吸血={flat_lifesteal}, 恢复={actual_heal}")

        except Exception as e:
            logger.error(f"处理吸血效果时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def monster_attack(self):
        """怪物攻击玩家"""
        if not self.battle_active or not self.player or not self.monster:
            return

        try:
            # 检查命中率
            monster_accuracy = getattr(self.monster, 'accuracy', 50)
            player_agility = getattr(self.player, 'agility', 10)
            hit_rate = DamageCalculator.calculate_hit_rate(monster_accuracy, player_agility)

            if random.random() > hit_rate:
                # 攻击未命中
                self.add_log(f"{self.monster.name} 的攻击被 {self.player.character_class} 闪避了！")
                self.monster_attacks += 1  # 记录攻击次数，即使未命中
                return

            # 使用统一的伤害计算系统
            damage, is_crit = DamageCalculator.calculate_damage(self.monster, self.player, "physical")

            # 确定攻击目标 - 优先攻击召唤物的逻辑
            target_is_summon = False
            target_summon = None
            target_is_charmed = False
            target_charmed = None

            # 检查玩家是否有召唤物或魅惑怪物
            has_targets = False
            active_summons = []
            active_charmed = []

            # 检查召唤物
            if hasattr(self.player, 'summons') and self.player.summons:
                # 过滤出活着的召唤物
                active_summons = [summon for summon in self.player.summons if not getattr(summon, 'is_dead', False) and not summon.is_expired()]
                if active_summons:
                    has_targets = True

            # 检查魅惑怪物
            if hasattr(self.player, 'charmed_monsters') and self.player.charmed_monsters:
                # 过滤出活着的魅惑怪物
                active_charmed = [monster for monster in self.player.charmed_monsters if not getattr(monster, 'is_dead', False)]
                if active_charmed:
                    has_targets = True

            if has_targets:
                # 70%概率攻击召唤物或魅惑怪物
                if random.random() < 0.7:
                    # 决定攻击召唤物还是魅惑怪物
                    all_targets = active_summons + active_charmed
                    target = random.choice(all_targets)

                    # 判断目标类型
                    if target in active_summons:
                        target_is_summon = True
                        target_summon = target
                        self.add_log(f"{self.monster.name} 决定攻击召唤物 {target_summon.name}")
                    else:
                        target_is_charmed = True
                        target_charmed = target
                        self.add_log(f"{self.monster.name} 决定攻击被魅惑的 {target_charmed.name}")

            if target_is_summon and target_summon:
                # 攻击召唤物
                # 应用召唤物的防御减伤
                summon_defense = getattr(target_summon, 'defense', 0)
                final_damage = max(1, damage - summon_defense)

                # 记录日志
                crit_text = "暴击！" if is_crit else ""
                self.add_log(f"{self.monster.name} {crit_text}攻击 {target_summon.name} 造成 {final_damage} 点伤害")

                # 应用伤害
                target_summon.hp = max(0, target_summon.hp - final_damage)

                # 更新统计
                self.monster_damage_dealt += final_damage
                self.monster_attacks += 1

                # 检查召唤物是否死亡
                if target_summon.hp <= 0:
                    target_summon.is_dead = True
                    self.add_log(f"{target_summon.name} 被击败!")
                else:
                    # 显示召唤物剩余血量
                    self.add_log(f"{target_summon.name} 剩余生命值: {target_summon.hp}/{target_summon.max_hp}")
            elif target_is_charmed and target_charmed:
                # 攻击魅惑怪物
                # 应用魅惑怪物的防御减伤
                charmed_defense = getattr(target_charmed, 'defense', 0)
                final_damage = max(1, damage - charmed_defense)

                # 记录日志
                crit_text = "暴击！" if is_crit else ""
                self.add_log(f"{self.monster.name} {crit_text}攻击 {target_charmed.name} 造成 {final_damage} 点伤害")

                # 应用伤害
                target_charmed.hp = max(0, target_charmed.hp - final_damage)

                # 更新统计
                self.monster_damage_dealt += final_damage
                self.monster_attacks += 1

                # 检查魅惑怪物是否死亡
                if target_charmed.hp <= 0:
                    target_charmed.is_dead = True
                    self.add_log(f"{target_charmed.name} 被击败!")

                    # 从玩家的魅惑怪物列表中移除死亡的怪物
                    if hasattr(self.player, 'charmed_monsters'):
                        if target_charmed in self.player.charmed_monsters:
                            self.player.charmed_monsters.remove(target_charmed)
                            logger.info(f"已从玩家的魅惑怪物列表中移除死亡的 {target_charmed.name}")
                else:
                    # 显示魅惑怪物剩余血量
                    self.add_log(f"{target_charmed.name} 剩余生命值: {target_charmed.hp}/{target_charmed.max_hp}")
            else:
                # 攻击玩家
                # 应用防御减伤 - 使用玩家的防御值
                player_defense = self.player.defense  # 直接获取防御值，不再尝试作为数组访问
                final_damage = max(1, damage - player_defense)

                # 记录日志
                crit_text = "暴击！" if is_crit else ""
                self.add_log(f"{self.monster.name} {crit_text}攻击 {self.player.character_class} 造成 {final_damage} 点伤害 (防御: {player_defense})") # 添加防御信息到日志

                # 应用伤害并更新统计
                self.player.hp = max(0, self.player.hp - final_damage)
                self.monster_damage_dealt += final_damage
                self.monster_attacks += 1

                # 触发伤害回调
                for callback in self.callbacks.get("on_damage_taken", []):
                    try:
                        callback(self.player, self.monster, final_damage)
                    except Exception as e:
                        logger.error(f"伤害回调执行失败: {e}")

                # 检查玩家是否已死亡
                if self.player and self.player.hp <= 0:
                    self.handle_player_death()
                    return  # 玩家死亡后直接返回
                elif self.player:
                    # 显示玩家剩余血量
                    self.add_log(f"{self.player.character_class} 剩余生命值: {self.player.hp}/{self.player.max_hp}")
                else:
                    logger.warning("玩家对象为None，无法处理怪物攻击结果")

        except Exception as e:
            logger.error(f"怪物攻击时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _get_random_tier_by_weight(self) -> str:
        """根据权重随机选择一个装备品质"""
        tiers_config = GameConfig.loaded_equipment.get("tiers", {})
        if not tiers_config:
            logger.warning("未找到装备品质配置，默认为'普通'")
            return "普通"

        tiers_with_weights = []
        for tier_name, tier_data in tiers_config.items():
            weight = tier_data.get("weight", 0)
            if weight > 0:
                tiers_with_weights.append((tier_name, weight))

        if not tiers_with_weights:
            logger.warning("装备品质配置中没有有效权重，默认为'普通'")
            return "普通"

        # 根据权重随机选择品质
        chosen_tier = random.choices([t[0] for t in tiers_with_weights], weights=[t[1] for t in tiers_with_weights], k=1)[0]
        return chosen_tier

    def handle_monster_death(self):
        """处理怪物死亡"""
        if not self.battle_active or not self.monster or not self.player:
            return

        # 清除怪物的所有效果
        if hasattr(self.monster, "reset_effects"):
            self.monster.reset_effects()

        # 记录怪物死亡日志
        self.add_log(f"{self.monster.name} 被击杀!")

        # 计算经验值（仅用于日志显示和统计，不直接给玩家增加经验）
        exp_gained = self.calculate_exp_gain(self.player.level, self.monster.exp, self.monster.level)
        self.add_log(f"获得了 {exp_gained} 经验值")

        # 只更新统计数据，不直接给玩家增加经验（经验将通过回调在Game类中处理）
        self.exp_gained += exp_gained

        # 计算金币（仅用于日志显示和统计，不直接给玩家增加金币）
        gold_base = self.monster.level * 5
        gold_random = random.randint(0, self.monster.level * 3)
        gold_gained = gold_base + gold_random

        # 应用VIP金币加成
        if hasattr(self.player, 'get_vip_drop_rate_bonus'):
            gold_bonus_percent = self.player.get_vip_drop_rate_bonus()
            if gold_bonus_percent > 0:
                bonus_gold = int(gold_gained * (gold_bonus_percent / 100))
                gold_gained += bonus_gold
                self.add_log(f"VIP加成: +{bonus_gold}金币({gold_bonus_percent}%)")

        # 只更新统计数据，不直接给玩家增加金币（金币将通过回调在Game类中处理）
        self.gold_gained += gold_gained
        self.add_log(f"获得了 {gold_gained} 金币")

        # 装备掉落逻辑
        try:
            # 1. 首先从掉落率配置中获取怪物的掉落物品
            config_drops = []
            if hasattr(GameConfig, 'DROP_RATES') and GameConfig.DROP_RATES:
                # 获取掉落率配置中的怪物掉落数据
                monster_drops = GameConfig.DROP_RATES.get('monsters', {}).get(self.monster.name, {}).get('drops', [])
                if monster_drops:
                    logger.info(f"从掉落率配置中找到 {self.monster.name} 的掉落物品，共 {len(monster_drops)} 件")
                    config_drops = monster_drops

            # 2. 处理从配置中获取的掉落物品
            if config_drops:
                for drop_info in config_drops:
                    item_name = drop_info.get('item')
                    drop_rate = drop_info.get('rate', 0)

                    if not item_name or drop_rate <= 0:
                        continue

                    # 应用玩家幸运值加成(每点幸运值增加0.5%掉落率)
                    if hasattr(self.player, 'luck'):
                        luck_bonus = self.player.luck * 0.005
                        drop_rate += luck_bonus

                    # 应用VIP掉落率加成
                    if hasattr(self.player, 'get_vip_drop_rate_bonus'):
                        vip_bonus = self.player.get_vip_drop_rate_bonus() / 100
                        drop_rate += vip_bonus

                    # 判定是否掉落
                    if random.random() < drop_rate:
                        # 获取物品信息
                        item_info = GameConfig.get_item_info(item_name)
                        if item_info:
                            # 复制一份物品信息，避免修改原始数据
                            dropped_item = item_info.copy()

                            # 调用品质生成系统来生成完整的品质和属性加成
                            dropped_item = self._generate_equipment_quality(dropped_item)

                            # 确保物品有类型
                            if 'type' not in dropped_item or not dropped_item['type']:
                                # 尝试从GameConfig推断类型
                                if hasattr(GameConfig, '_infer_item_type'):
                                    inferred_type = GameConfig._infer_item_type(dropped_item['name'])
                                    if inferred_type:
                                        dropped_item['type'] = inferred_type
                                    else:
                                        # 默认为武器类型
                                        dropped_item['type'] = "武器"
                                else:
                                    # 默认为武器类型
                                    dropped_item['type'] = "武器"

                            # 添加到玩家背包
                            if self.player.add_item(dropped_item):
                                self.add_log(f"掉落装备: [{dropped_item.get('tier', '普通')}]{dropped_item['name']}")
                                logger.info(f"玩家获得掉落装备: [{dropped_item.get('tier', '普通')}]{dropped_item['name']}")
                                self.equipment_dropped += 1
                            else:
                                self.add_log("背包已满，无法获取掉落装备")
                                logger.warning("玩家背包已满，无法获取掉落装备")
                        else:
                            logger.warning(f"找不到物品信息: {item_name}")

            # 3. 获取怪物对象中的特定掉落物
            monster_specific_drops = getattr(self.monster, 'possible_drops', [])

            # 如果怪物有特定掉落，处理这些掉落
            if monster_specific_drops:
                logger.info(f"处理怪物特定掉落，共 {len(monster_specific_drops)} 件装备")
                for potential_item in monster_specific_drops:
                    # 计算基础掉落概率 - 每件装备基础5%概率
                    base_drop_chance = 0.05

                    # 应用玩家幸运值加成(每点幸运值增加0.5%掉落率)
                    if hasattr(self.player, 'luck'):
                        luck_bonus = self.player.luck * 0.005
                        base_drop_chance += luck_bonus

                    # 应用VIP掉落率加成
                    if hasattr(self.player, 'get_vip_drop_rate_bonus'):
                        vip_bonus = self.player.get_vip_drop_rate_bonus() / 100
                        base_drop_chance += vip_bonus

                    # 单独判定该装备是否掉落
                    if random.random() < base_drop_chance:
                        dropped_item = potential_item.copy()
                        # 调用品质生成系统来生成完整的品质和属性加成
                        dropped_item = self._generate_equipment_quality(dropped_item)

                        # 确保物品有类型
                        if 'type' not in dropped_item or not dropped_item['type']:
                            # 尝试从GameConfig推断类型
                            if hasattr(GameConfig, '_infer_item_type'):
                                inferred_type = GameConfig._infer_item_type(dropped_item['name'])
                                if inferred_type:
                                    dropped_item['type'] = inferred_type
                                else:
                                    # 默认为武器类型
                                    dropped_item['type'] = "武器"
                            else:
                                # 默认为武器类型
                                dropped_item['type'] = "武器"

                        if self.player.add_item(dropped_item):
                            self.add_log(f"掉落装备: [{dropped_item.get('tier', '普通')}]{dropped_item['name']}")
                            logger.info(f"玩家获得掉落装备: [{dropped_item.get('tier', '普通')}]{dropped_item['name']}")
                            self.equipment_dropped += 1
                        else:
                            self.add_log("背包已满，无法获取掉落装备")
                            logger.warning("玩家背包已满，无法获取掉落装备")

            # 4. 处理通用装备掉落（如果前两种方式都没有掉落装备）
            if self.equipment_dropped == 0 and hasattr(GameConfig, 'loaded_equipment'):
                equipment_db = GameConfig.loaded_equipment.get("equipment_db", {})

                # 获取所有符合怪物等级的装备
                eligible_equipment = []
                for category, items in equipment_db.items():
                    for item in items:
                        # 确保只选择等级符合的装备，并且排除新手装备（因为它们通常有特定掉落或不适合通用掉落）
                        if item.get("level", 0) <= self.monster.level + 5 and category != "新手装备":
                            eligible_equipment.append(item)

                if eligible_equipment:
                    # 从符合条件的装备中随机选择一件基础装备
                    base_item = random.choice(eligible_equipment).copy()

                    # 调用品质生成系统来生成完整的品质和属性加成
                    base_item = self._generate_equipment_quality(base_item)

                    # 确保物品有类型
                    if 'type' not in base_item or not base_item['type']:
                        # 尝试从GameConfig推断类型
                        if hasattr(GameConfig, '_infer_item_type'):
                            inferred_type = GameConfig._infer_item_type(base_item['name'])
                            if inferred_type:
                                base_item['type'] = inferred_type
                            else:
                                # 默认为武器类型
                                base_item['type'] = "武器"
                        else:
                            # 默认为武器类型
                            base_item['type'] = "武器"

                    # 计算掉落概率（通用掉落的基础概率可以调整）
                    general_drop_chance = 0.1 # 例如，通用装备基础掉落率为10%
                    if hasattr(self.player, 'luck'):
                        general_drop_chance += self.player.luck * 0.005
                    if hasattr(self.player, 'get_vip_drop_rate_bonus'):
                        general_drop_chance += self.player.get_vip_drop_rate_bonus() / 100

                    if random.random() < general_drop_chance:
                        if self.player.add_item(base_item):
                            self.add_log(f"掉落装备: [{base_item['tier']}]{base_item['name']}")
                            logger.info(f"玩家获得掉落装备: [{base_item['tier']}]{base_item['name']}")
                            self.equipment_dropped += 1
                        else:
                            self.add_log("背包已满，无法获取掉落装备")
                            logger.warning("玩家背包已满，无法获取掉落装备")
                else:
                    logger.info("没有符合怪物等级的通用装备可供掉落。")

        except Exception as e:
            logger.error(f"处理装备掉落时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # 触发怪物死亡回调
        for callback in self.callbacks.get("on_monster_death", []):
            try:
                callback(self.player, self.monster)
            except Exception as e:
                logger.error(f"怪物死亡回调执行失败: {e}")

        # 结束战斗
        self.end_battle(True)

    def calculate_exp_gain(self, player_level: int, base_exp: int, monster_level: int) -> int:
        """
        根据玩家和怪物的等级差计算实际获得的经验值

        参数:
            player_level: 玩家等级
            base_exp: 怪物基础经验值
            monster_level: 怪物等级

        返回:
            int: 实际获得的经验值
        """
        # 计算等级差（玩家等级 - 怪物等级）
        level_diff = player_level - monster_level

        # 等级差在10级以内（包括10级），不衰减经验
        if level_diff <= 10:
            return base_exp

        # 等级差在11-25级之间，按比例衰减
        elif level_diff <= 25:
            # 计算衰减比例：每超过10级差1级，减少1/15的经验
            reduction_ratio = (level_diff - 10) / 15
            exp_ratio = 1 - reduction_ratio
            # 确保至少有1点经验（四舍五入取整）
            return max(1, round(base_exp * exp_ratio))

        # 等级差超过25级，获得1点经验
        else:
            return 1

    def handle_player_death(self):
        """处理玩家死亡"""
        if not self.battle_active or self.player is None: # 确保player不是None
            return

        # 记录玩家死亡日志
        self.add_log(f"{self.player.character_class} 被 {self.monster.name} 击败!")

        # 触发玩家死亡回调
        for callback in self.callbacks.get("on_player_death", []):
            try:
                callback(self.player, self.monster)
            except Exception as e:
                logger.error(f"玩家死亡回调执行失败: {e}")

        # 结束战斗
        self.end_battle(False)

        # 不再立即恢复玩家生命值，而是由Game类管理复活过程
        # 设置死亡状态
        if self.player:
            self.player.is_dead = True
            self.player.last_death_time = time.time()
            self.add_log(f"{self.player.character_class} 将在10秒后自动复活")
        else:
            logger.warning("玩家对象为None，无法设置死亡状态")

    def add_log(self, message: str):
        """添加战斗日志"""
        if "掉落" in message or "获得" in message or "造成" in message:
            # 记录重要的战斗信息
            logger.info(f"战斗日志: {message}")

        # 添加到日志列表
        self.battle_logs.append(message)

        # 限制日志列表大小
        if len(self.battle_logs) > self.max_log_entries:
            self.battle_logs = self.battle_logs[-self.max_log_entries:]

        # 触发日志回调
        for callback in self.callbacks.get("on_battle_log", []):
            try:
                callback(message)
            except Exception as e:
                logger.error(f"日志回调执行失败: {e}")

    def toggle_auto_battle(self) -> bool:
        """切换自动战斗状态"""
        self.auto_battle = not self.auto_battle
        return self.auto_battle

    def is_in_battle(self) -> bool:
        """
        检查是否处于战斗状态

        返回:
            bool: 是否处于战斗状态
        """
        return self.battle_active and self.player is not None and self.monster is not None

    def get_battle_stats(self) -> Dict[str, Any]:
        """获取战斗统计信息"""
        if not self.player or not self.monster:
            return {}

        return {
            "player": {
                "name": self.player.character_class,
                "hp": self.player.hp,
                "max_hp": self.player.max_hp,
                "attack": self.player.attack,
                "defense": self.player.defense
            },
            "monster": {
                "name": self.monster.name,
                "hp": self.monster.hp,
                "max_hp": self.monster.max_hp,
                "attack": f"{self.monster.attack_range['min']}-{self.monster.attack_range['max']}",
                "defense": self.monster.defense
            },
            "battle_time": time.time() - self.battle_start_time,
            "auto_battle": self.auto_battle,
            "stats": {
                "player_damage_dealt": self.player_damage_dealt,
                "player_attacks": self.player_attacks,
                "player_crit_rate": self.player_crits / self.player_attacks if self.player_attacks > 0 else 0,
                "monster_damage_dealt": self.monster_damage_dealt,
                "monster_attacks": self.monster_attacks,
                # 添加新的统计项目
                "summon_damage_dealt": self.summon_damage_dealt,
                "summon_attacks": self.summon_attacks,
                "poison_damage": self.poison_damage,
                "ground_effect_damage": self.ground_effect_damage,
                "total_damage_dealt": self.player_damage_dealt + self.summon_damage_dealt + self.poison_damage + self.ground_effect_damage,
                "exp_gained": self.exp_gained,
                "gold_gained": self.gold_gained,
                "equipment_dropped": self.equipment_dropped
            }
        }

    def update_summons(self):
        """更新召唤物战斗状态"""
        # 如果战斗未激活或玩家/怪物不存在，则直接返回
        if not self.battle_active or not self.player or not self.monster:
            return

        # 检查玩家是否有召唤物
        if not hasattr(self.player, 'summons') or not self.player.summons:
            return

        # 获取当前时间
        current_time = time.time()

        # 过滤掉已过期的召唤物
        active_summons = []
        for summon in self.player.summons[:]:  # 使用副本，避免在循环中修改列表
            # 首先更新召唤物状态
            if hasattr(summon, 'update'):
                # 如果update返回False，表示召唤物已过期或死亡
                if not summon.update(current_time):
                    logger.info(f"召唤物 {summon.name} 已过期或死亡，从列表中移除")
                    continue
            # 兼容旧版本：检查召唤物是否过期
            elif hasattr(summon, 'is_expired') and summon.is_expired():
                logger.info(f"召唤物 {summon.name} 已过期，从列表中移除")
                continue

            # 检查召唤物是否死亡
            if hasattr(summon, 'is_dead') and summon.is_dead:
                logger.info(f"召唤物 {summon.name} 已死亡，从列表中移除")
                continue

            # 召唤物有效，添加到活跃列表
            active_summons.append(summon)

        # 更新玩家的召唤物列表
        if len(active_summons) != len(self.player.summons):
            self.player.summons = active_summons
            logger.info(f"更新玩家召唤物列表，当前有 {len(active_summons)} 个活跃召唤物")

        # 让每个活跃召唤物尝试攻击
        for summon in active_summons:
            # 处理召唤物攻击
            self.summon_attack(summon, current_time)

    def summon_attack(self, summon, current_time):
        """处理召唤物攻击"""
        try:
            # 检查召唤物攻击间隔
            if not hasattr(summon, 'last_attack_time'):
                summon.last_attack_time = current_time
                return

            # 计算攻击间隔
            attack_interval = getattr(summon, 'attack_interval', 2.0)

            # 如果未到攻击时间，则跳过
            time_since_last_attack = current_time - summon.last_attack_time
            if time_since_last_attack < attack_interval:
                return

            # 更新上次攻击时间
            summon.last_attack_time = current_time

            # 使用召唤物自身的攻击方法（如果存在）
            if hasattr(summon, 'attack_monster') and callable(summon.attack_monster):
                # 使用召唤物的专用攻击方法
                damage, is_crit = summon.attack_monster(self.monster)

                # 更新召唤物攻击统计
                self.summon_attacks += 1
                self.summon_damage_dealt += damage

                # 检查怪物是否死亡
                if self.monster.hp <= 0:
                    logger.info(f"怪物被召唤物{summon.name}击败")
                    # 怪物死亡在execute_battle_round方法中处理
                return

            # 以下是默认攻击逻辑，当召唤物没有自己的攻击方法时使用

            # 使用统一的命中率计算
            summon_accuracy = getattr(summon, 'accuracy', 70 + summon.level * 2)
            monster_agility = getattr(self.monster, 'agility', self.monster.level)
            hit_rate = DamageCalculator.calculate_hit_rate(summon_accuracy, monster_agility)

            # 进行命中判定
            if random.random() > hit_rate:
                # 攻击未命中
                self.add_log(f"{summon.name}的攻击被{self.monster.name}闪避了！")
                logger.debug(f"召唤物攻击未命中: {summon.name} -> {self.monster.name}")
                self.summon_attacks += 1  # 记录攻击次数，即使未命中
                return

            # 使用统一的伤害计算系统
            final_damage, is_crit = DamageCalculator.calculate_damage(summon, self.monster, "physical")

            # 应用伤害
            self.monster.hp = max(0, self.monster.hp - final_damage)

            # 更新统计
            self.summon_attacks += 1
            self.summon_damage_dealt += final_damage

            # 添加战斗日志
            crit_text = "暴击！" if is_crit else ""
            message = f"{summon.name} {crit_text}对 {self.monster.name} 造成 {final_damage} 点伤害！"
            self.add_log(message)
            logger.info(f"召唤物攻击: {message}")

            # 检查怪物是否死亡
            if self.monster.hp <= 0:
                logger.info(f"怪物被召唤物{summon.name}击败")
                # 怪物死亡在execute_battle_round方法中处理
        except Exception as e:
            logger.error(f"召唤物攻击时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    def _update_poison_effects(self, current_time: float):
        """处理毒药效果"""
        if not self.battle_active or not self.monster:
            return

        # 检查怪物是否有中毒效果
        poison_effects = getattr(self.monster, "poison_effects", [])
        if not poison_effects:
            return

        # 处理每个毒药效果
        new_poison_effects = []
        total_poison_damage = 0

        for effect in poison_effects:
            # 检查是否需要造成伤害（每秒一次）
            if current_time - effect.get("last_tick_time", 0) >= 1.0:
                # 更新上次造成伤害的时间
                effect["last_tick_time"] = current_time

                # 减少剩余持续时间
                effect["remaining_duration"] -= 1

                # 如果剩余时间大于0，造成伤害并保留效果
                if effect["remaining_duration"] > 0:
                    damage = effect["damage_per_tick"]
                    total_poison_damage += damage
                    new_poison_effects.append(effect)
            else:
                # 如果还不到一秒，保留效果
                new_poison_effects.append(effect)

        # 更新怪物的毒药效果列表
        self.monster.poison_effects = new_poison_effects

        # 如果有毒药伤害需要应用
        if total_poison_damage > 0:
            # 应用伤害
            self.monster.hp = max(0, self.monster.hp - total_poison_damage)

            # 更新统计
            self.poison_damage += total_poison_damage

            # 添加战斗日志
            self.add_log(f"{self.monster.name} 受到 {total_poison_damage} 点持续伤害！剩余生命值: {self.monster.hp}/{self.monster.max_hp}")

            # 检查怪物是否死亡
            if self.monster.hp <= 0:
                self.add_log(f"{self.monster.name} 死于持续伤害！")

    def add_ground_effect(self, effect_data):
        """添加地面效果

        参数:
            effect_data: 地面效果数据，包含以下字段：
                - type: 效果类型（如"damage"）
                - damage_per_tick: 每次伤害值
                - remaining_duration: 剩余持续时间（秒）
                - last_tick_time: 上次触发时间
                - skill_name: 技能名称
        """
        if not self.player or not hasattr(self.player, "game"):
            logger.warning("无法添加地面效果：玩家对象不存在或没有game属性")
            return False

        # 确保game对象有ground_effects属性
        if not hasattr(self.player.game, "ground_effects"):
            self.player.game.ground_effects = []

        # 添加效果
        self.player.game.ground_effects.append(effect_data)
        logger.info(f"添加地面效果: {effect_data['skill_name']}, 持续 {effect_data['remaining_duration']} 秒")
        return True

    def update_charmed_monsters(self):
        """更新魅惑怪物战斗状态"""
        # 如果战斗未激活或玩家/怪物不存在，则直接返回
        if not self.battle_active or not self.player or not self.monster:
            return

        # 检查玩家是否有魅惑怪物
        if not hasattr(self.player, 'charmed_monsters') or not self.player.charmed_monsters:
            return

        # 获取当前时间
        current_time = time.time()

        # 过滤掉已死亡的魅惑怪物
        active_charmed = []
        for charmed in self.player.charmed_monsters[:]:  # 使用副本，避免在循环中修改列表
            # 检查魅惑怪物是否死亡
            if hasattr(charmed, 'is_dead') and charmed.is_dead:
                logger.info(f"魅惑怪物 {charmed.name} 已死亡，从列表中移除")
                continue

            # 检查魅惑怪物是否过期（如果有持续时间）
            if hasattr(charmed, 'charmed_duration') and hasattr(charmed, 'charmed_time'):
                # 持续时间为-1表示永久生效，不检查过期
                if charmed.charmed_duration != -1:
                    time_elapsed = current_time - charmed.charmed_time
                    if time_elapsed >= charmed.charmed_duration:
                        logger.info(f"魅惑怪物 {charmed.name} 魅惑效果已过期，从列表中移除")
                        continue

            # 魅惑怪物有效，添加到活跃列表
            active_charmed.append(charmed)

        # 更新玩家的魅惑怪物列表
        if len(active_charmed) != len(self.player.charmed_monsters):
            self.player.charmed_monsters = active_charmed
            logger.info(f"更新玩家魅惑怪物列表，当前有 {len(active_charmed)} 个活跃魅惑怪物")

        # 让每个活跃魅惑怪物尝试攻击
        for charmed in active_charmed:
            # 处理魅惑怪物攻击
            self.charmed_monster_attack(charmed, current_time)

    def charmed_monster_attack(self, charmed, current_time):
        """处理魅惑怪物攻击"""
        try:
            # 检查魅惑怪物攻击间隔
            if not hasattr(charmed, 'last_attack_time'):
                charmed.last_attack_time = current_time
                return

            # 计算攻击间隔
            attack_interval = getattr(charmed, 'attack_interval', 2.0)

            # 如果未到攻击时间，则跳过
            time_since_last_attack = current_time - charmed.last_attack_time
            if time_since_last_attack < attack_interval:
                return

            # 更新上次攻击时间
            charmed.last_attack_time = current_time

            # 使用统一的命中率计算
            charmed_accuracy = getattr(charmed, 'accuracy', 70 + charmed.level * 2)
            monster_agility = getattr(self.monster, 'agility', self.monster.level)
            hit_rate = DamageCalculator.calculate_hit_rate(charmed_accuracy, monster_agility)

            # 进行命中判定
            if random.random() > hit_rate:
                # 攻击未命中
                self.add_log(f"被魅惑的 {charmed.name} 的攻击被 {self.monster.name} 闪避了！")
                logger.debug(f"魅惑怪物攻击未命中: {charmed.name} -> {self.monster.name}")
                return

            # 使用统一的伤害计算系统
            final_damage, is_crit = DamageCalculator.calculate_damage(charmed, self.monster, "physical")

            # 应用伤害
            self.monster.hp = max(0, self.monster.hp - final_damage)

            # 添加战斗日志
            crit_text = "暴击！" if is_crit else ""
            message = f"被魅惑的 {charmed.name} {crit_text}对 {self.monster.name} 造成 {final_damage} 点伤害！"
            self.add_log(message)
            logger.info(f"魅惑怪物攻击: {message}")

            # 检查怪物是否死亡
            if self.monster.hp <= 0:
                logger.info(f"怪物被魅惑怪物 {charmed.name} 击败")
                # 怪物死亡在execute_battle_round方法中处理
        except Exception as e:
            logger.error(f"魅惑怪物攻击时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _update_ground_effects(self, current_time: float):
        """处理地面效果"""
        # 检查游戏管理器是否有地面效果
        if not self.player or not hasattr(self.player, "game"):
            return

        if not hasattr(self.player.game, "ground_effects"):
            self.player.game.ground_effects = []
            return

        # 获取所有地面效果
        ground_effects = self.player.game.ground_effects
        if not ground_effects:
            return

        # 处理每个地面效果
        new_effects = []

        for effect in ground_effects:
            # 检查效果是否过期
            if effect["remaining_duration"] <= 0:
                continue

            # 检查是否需要造成伤害（每秒一次）
            if current_time - effect.get("last_tick_time", 0) >= 1.0:
                # 更新上次造成伤害的时间
                effect["last_tick_time"] = current_time

                # 减少剩余持续时间
                effect["remaining_duration"] -= 1

                # 如果剩余时间大于0，造成伤害并保留效果
                if effect["remaining_duration"] > 0:
                    # 检查怪物是否在效果范围内
                    # 简化：假设怪物总是在范围内
                    monster = self.monster
                    if monster and effect["type"] == "damage":
                        damage = effect["damage_per_tick"]
                        monster.hp = max(0, monster.hp - damage)
                        self.add_log(f"{monster.name} 受到 {effect['skill_name']} 的 {damage} 点伤害！剩余生命值: {monster.hp}/{monster.max_hp}")

                        # 更新地面效果伤害统计
                        self.ground_effect_damage += damage

                    # 保留效果
                    new_effects.append(effect)
            else:
                # 效果未触发，保留
                new_effects.append(effect)

        # 更新地面效果列表
        self.player.game.ground_effects = new_effects

    def _generate_equipment_quality(self, base_eq):
        """为装备生成随机品质和属性加成

        Args:
            base_eq: 基础装备数据

        Returns:
            dict: 带有品质和加成的装备数据
        """
        try:
            # 复制装备数据，避免修改原始数据
            base_eq = base_eq.copy()

            # 获取品质配置
            tiers_config = GameConfig.EQUIPMENT.get("tiers", {})
            tiers = list(tiers_config.keys()) or ["普通", "精良", "稀有", "史诗", "传说"]
            default_weights = [160, 34, 20, 5, 1]  # 普通, 精良, 稀有, 史诗, 传说的默认权重

            tier = "普通"  # 默认品质
            quality_bonus_points = 0  # 默认加成点数

            try:
                weights = [tiers_config.get(t, {}).get("weight", d) for t, d in zip(tiers, default_weights)]

                # 使用权重随机选择品质
                tier = random.choices(tiers, weights=weights, k=1)[0]

                # 品质对应的加成点数
                quality_bonus_points = {
                    "普通": 0,
                    "精良": 1,  # 固定1点
                    "稀有": 2,   # 固定2点
                    "史诗": 3,   # 固定3点
                    "传说": 4    # 固定4点
                }.get(tier, 0)
            except (KeyError, ValueError, IndexError) as e:
                logger.error(f"品质系统配置错误: {str(e)}")
                tier = "普通"
                quality_bonus_points = 0

            # 可用属性列表
            available_attrs = []

            # 检查装备有哪些属性可以加成
            if "attack" in base_eq or "atk" in base_eq:
                available_attrs.append("attack")
            if "defense" in base_eq or "def" in base_eq:
                available_attrs.append("defense")
            if "magic_defense" in base_eq or "mdef" in base_eq:
                available_attrs.append("magic_defense")
            if "magic" in base_eq:
                available_attrs.append("magic")
            if "taoism" in base_eq:
                available_attrs.append("taoism")

            # 如果没有可加成属性，添加默认属性
            if not available_attrs:
                if base_eq.get("type", "").lower() in ["武器", "weapon"]:
                    available_attrs = ["attack"]
                elif base_eq.get("type", "").lower() in ["防具", "armor"]:
                    available_attrs = ["defense", "magic_defense"]
                else:
                    available_attrs = ["attack", "defense", "magic_defense", "magic", "taoism"]

            # 随机分配加成点数
            bonus_distribution = {}
            for _ in range(quality_bonus_points):
                attr = random.choice(available_attrs)
                bonus_distribution[attr] = bonus_distribution.get(attr, 0) + 1

            # 应用加成
            for attr, bonus in bonus_distribution.items():
                if attr == "attack":
                    if "attack" in base_eq:
                        if isinstance(base_eq["attack"], list) and len(base_eq["attack"]) >= 2:
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["attack"] = [base_eq["attack"][0] + bonus, base_eq["attack"][1]]
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["attack"] = [base_eq["attack"][0], base_eq["attack"][1] + bonus]
                        else:
                            base_eq["attack"] = base_eq["attack"] + bonus
                    elif "atk" in base_eq:
                        if isinstance(base_eq["atk"], tuple) and len(base_eq["atk"]) >= 2:
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["atk"] = (base_eq["atk"][0] + bonus, base_eq["atk"][1])
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["atk"] = (base_eq["atk"][0], base_eq["atk"][1] + bonus)
                        else:
                            base_eq["atk"] = base_eq["atk"] + bonus
                    else:
                        base_eq["attack"] = [bonus, bonus]

                elif attr == "defense":
                    if "defense" in base_eq:
                        if isinstance(base_eq["defense"], list) and len(base_eq["defense"]) >= 2:
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["defense"] = [base_eq["defense"][0] + bonus, base_eq["defense"][1]]
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["defense"] = [base_eq["defense"][0], base_eq["defense"][1] + bonus]
                        else:
                            base_eq["defense"] = base_eq["defense"] + bonus
                    elif "def" in base_eq:
                        if isinstance(base_eq["def"], tuple) and len(base_eq["def"]) >= 2:
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["def"] = (base_eq["def"][0] + bonus, base_eq["def"][1])
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["def"] = (base_eq["def"][0], base_eq["def"][1] + bonus)
                        else:
                            base_eq["def"] = base_eq["def"] + bonus
                    else:
                        base_eq["defense"] = [bonus, bonus]

                elif attr == "magic_defense":
                    if "magic_defense" in base_eq:
                        if isinstance(base_eq["magic_defense"], list) and len(base_eq["magic_defense"]) >= 2:
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["magic_defense"] = [base_eq["magic_defense"][0] + bonus, base_eq["magic_defense"][1]]
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["magic_defense"] = [base_eq["magic_defense"][0], base_eq["magic_defense"][1] + bonus]
                        else:
                            base_eq["magic_defense"] = base_eq["magic_defense"] + bonus
                    elif "mdef" in base_eq:
                        if isinstance(base_eq["mdef"], tuple) and len(base_eq["mdef"]) >= 2:
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["mdef"] = (base_eq["mdef"][0] + bonus, base_eq["mdef"][1])
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["mdef"] = (base_eq["mdef"][0], base_eq["mdef"][1] + bonus)
                        else:
                            base_eq["mdef"] = base_eq["mdef"] + bonus
                    else:
                        base_eq["magic_defense"] = [bonus, bonus]

                elif attr == "magic":
                    if "magic" in base_eq:
                        if isinstance(base_eq["magic"], list) and len(base_eq["magic"]) >= 2:
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["magic"] = [base_eq["magic"][0] + bonus, base_eq["magic"][1]]
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["magic"] = [base_eq["magic"][0], base_eq["magic"][1] + bonus]
                        else:
                            base_eq["magic"] = base_eq["magic"] + bonus
                    else:
                        base_eq["magic"] = [bonus, bonus]

                elif attr == "taoism":
                    if "taoism" in base_eq:
                        if isinstance(base_eq["taoism"], list) and len(base_eq["taoism"]) >= 2:
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["taoism"] = [base_eq["taoism"][0] + bonus, base_eq["taoism"][1]]
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["taoism"] = [base_eq["taoism"][0], base_eq["taoism"][1] + bonus]
                        else:
                            base_eq["taoism"] = base_eq["taoism"] + bonus
                    else:
                        base_eq["taoism"] = [bonus, bonus]

            # 保存品质加成分布信息，用于物品描述
            base_eq["quality_bonus"] = bonus_distribution

            try:
                # 添加品质信息
                base_eq.update({
                    "tier": tier,
                    "quality": tier,  # 同时设置quality属性，兼容界面显示
                    "equipped": False,
                    "price": random.randint(*tiers_config.get(tier, {}).get("price", [50, 100]))
                })
            except Exception as e:
                logger.error(f"更新品质信息时出错: {e}")
                # 使用默认值
                base_eq.update({
                    "tier": tier,
                    "quality": tier,  # 同时设置quality属性，兼容界面显示
                    "equipped": False,
                    "price": 50
                })

            # 添加特殊属性（仅史诗和传说品质）
            if tier in ["史诗", "传说"]:
                specials = ["暴击", "攻速"]
                if tier == "传说":
                    specials.extend(["生命偷取", "魔法穿透"])

                base_eq["special"] = random.choice(specials)

                if base_eq["special"] == "暴击":
                    base_eq["crit_damage"] = 0.3 if tier == "史诗" else 0.5
                elif base_eq["special"] == "攻速":
                    base_eq["attack_speed"] = 0.2 if tier == "史诗" else 0.3
                elif base_eq["special"] == "生命偷取":
                    base_eq["life_steal"] = 0.1
                elif base_eq["special"] == "魔法穿透":
                    base_eq["magic_penetration"] = 0.2

            # 为武器类装备添加额外的吸血属性几率（任何品质都有低概率获得）
            elif base_eq.get("type") == "武器" and random.random() < 0.05:  # 5%几率
                # 随机决定是百分比吸血还是固定值吸血
                lifesteal_type = random.choice(["百分比", "固定值"])

                if lifesteal_type == "百分比":
                    base_eq["special"] = "吸血"
                    # 根据品质决定吸血值
                    lifesteal_values = {
                        "普通": [1, 3],
                        "精良": [2, 4],
                        "稀有": [3, 5],
                        "史诗": [4, 6],
                        "传说": [5, 8]
                    }
                    min_val, max_val = lifesteal_values.get(tier, [1, 2])
                    base_eq["lifesteal"] = random.randint(min_val, max_val)
                else:
                    base_eq["special"] = "固定吸血"
                    # 根据品质决定固定吸血值
                    flat_lifesteal_values = {
                        "普通": [1, 2],
                        "精良": [2, 3],
                        "稀有": [3, 5],
                        "史诗": [4, 7],
                        "传说": [5, 10]
                    }
                    min_val, max_val = flat_lifesteal_values.get(tier, [1, 2])
                    base_eq["flat_lifesteal"] = random.randint(min_val, max_val)

            return base_eq

        except Exception as e:
            logger.error(f"生成装备品质时发生错误: {e}")
            # 确保在发生错误时也返回原装备
            base_eq["tier"] = "普通"
            base_eq["quality"] = "普通"
            return base_eq
