#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试死亡倒计时显示修复
验证死亡倒计时是否正确显示且不会刷屏
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_death_countdown_display():
    """测试死亡倒计时显示逻辑"""
    print("=" * 60)
    print("测试死亡倒计时显示修复")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.player import Player
        from core.game import Game
        from core.config import GameConfig
        
        # 初始化游戏配置
        GameConfig.initialize()
        
        print("1. 创建测试环境")
        
        # 模拟游戏界面的倒计时逻辑
        class MockGameScreen:
            def __init__(self):
                self.monster_components = {"hint": MockHintComponent()}
                self.game_manager = MockGameManager()
                
            def update_death_countdown(self, current_time):
                """模拟游戏界面的死亡倒计时更新逻辑"""
                if self.game_manager.player.is_dead:
                    # 计算剩余复活时间
                    death_time = float(self.game_manager.player.last_death_time)
                    remaining_time = max(0, 10.0 - (current_time - death_time))
                    remaining_seconds = int(remaining_time)

                    # 只在倒计时秒数变化时更新显示，避免刷屏
                    if not hasattr(self, '_last_death_countdown') or self._last_death_countdown != remaining_seconds:
                        self._last_death_countdown = remaining_seconds
                        if remaining_seconds > 0:
                            death_message_log = f"你已死亡！将在 {remaining_seconds} 秒后复活..."
                            self.game_manager.add_log(death_message_log, is_battle=True)
                            print(f"   📢 日志更新: {death_message_log}")
                        else:
                            self.game_manager.add_log("正在复活中...", is_battle=True)
                            print(f"   📢 日志更新: 正在复活中...")
                    
                    # 在怪物提示区域显示死亡倒计时
                    if remaining_seconds > 0:
                        hint_text = f"死亡状态 - {remaining_seconds}秒后复活"
                        self.monster_components["hint"].set_text(hint_text)
                        print(f"   💀 提示更新: {hint_text}")
                    else:
                        hint_text = "正在复活中..."
                        self.monster_components["hint"].set_text(hint_text)
                        print(f"   💀 提示更新: {hint_text}")
                else:
                    # 玩家复活时清除死亡倒计时显示
                    if hasattr(self, '_last_death_countdown'):
                        # 清除倒计时缓存
                        delattr(self, '_last_death_countdown')
                        # 清除怪物提示区域的死亡信息
                        hint_text = "无怪物"
                        self.monster_components["hint"].set_text(hint_text)
                        print(f"   ✅ 复活清理: {hint_text}")
        
        class MockHintComponent:
            def __init__(self):
                self.text = "无怪物"
            
            def set_text(self, text):
                self.text = text
        
        class MockGameManager:
            def __init__(self):
                self.player = Player("战士", "测试战士", "男")
                self.battle_logs = []
            
            def add_log(self, message, is_battle=False):
                self.battle_logs.append(message)
        
        # 创建模拟游戏界面
        game_screen = MockGameScreen()
        
        print("   游戏界面模拟创建成功")
        print(f"   初始提示: {game_screen.monster_components['hint'].text}")
        
        print("\n2. 模拟玩家死亡")
        game_screen.game_manager.player.is_dead = True
        game_screen.game_manager.player.last_death_time = time.time()
        print(f"   玩家死亡时间: {game_screen.game_manager.player.last_death_time}")
        
        print("\n3. 模拟倒计时更新（每0.5秒更新一次，持续12秒）")
        start_time = time.time()
        update_count = 0
        log_count_before = len(game_screen.game_manager.battle_logs)
        
        for i in range(24):  # 12秒，每0.5秒更新一次
            current_time = start_time + (i * 0.5)
            print(f"\n   --- 更新 #{i+1} (时间: +{i*0.5:.1f}秒) ---")
            
            # 更新倒计时
            game_screen.update_death_countdown(current_time)
            update_count += 1
            
            # 检查是否应该复活
            if current_time - game_screen.game_manager.player.last_death_time >= 10.0:
                if game_screen.game_manager.player.is_dead:
                    print(f"   🔄 玩家复活时间到，执行复活")
                    game_screen.game_manager.player.is_dead = False
                    game_screen.game_manager.player.hp = game_screen.game_manager.player.max_hp
            
            # 短暂延时以便观察
            time.sleep(0.1)
        
        log_count_after = len(game_screen.game_manager.battle_logs)
        
        print(f"\n4. 测试结果分析")
        print(f"   总更新次数: {update_count}")
        print(f"   日志增加数量: {log_count_after - log_count_before}")
        print(f"   最终提示状态: {game_screen.monster_components['hint'].text}")
        print(f"   玩家最终状态: {'活着' if not game_screen.game_manager.player.is_dead else '死亡'}")
        
        # 显示所有日志
        print(f"\n5. 生成的日志记录:")
        for i, log in enumerate(game_screen.game_manager.battle_logs[log_count_before:], 1):
            print(f"   {i}. {log}")
        
        # 验证结果
        expected_logs = 11  # 10秒倒计时 + 1个"正在复活中"
        actual_logs = log_count_after - log_count_before
        
        if actual_logs <= expected_logs + 2:  # 允许一些误差
            print(f"\n✅ 测试通过！日志数量合理 (期望≤{expected_logs+2}, 实际={actual_logs})")
            print("   - 倒计时正确显示")
            print("   - 没有重复刷屏")
            print("   - 复活后正确清理")
            return True
        else:
            print(f"\n❌ 测试失败！日志数量过多 (期望≤{expected_logs+2}, 实际={actual_logs})")
            print("   - 可能存在重复刷屏问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_countdown_cache_mechanism():
    """测试倒计时缓存机制"""
    print("\n" + "=" * 60)
    print("测试倒计时缓存机制")
    print("=" * 60)
    
    try:
        # 模拟游戏界面类
        class TestGameScreen:
            def __init__(self):
                pass
            
            def check_countdown_cache(self, remaining_seconds):
                """检查倒计时缓存逻辑"""
                should_update = False
                
                if not hasattr(self, '_last_death_countdown') or self._last_death_countdown != remaining_seconds:
                    self._last_death_countdown = remaining_seconds
                    should_update = True
                
                return should_update
        
        screen = TestGameScreen()
        
        print("1. 测试首次倒计时")
        result1 = screen.check_countdown_cache(10)
        print(f"   首次倒计时(10秒): 应该更新 = {result1}")
        
        print("\n2. 测试相同倒计时")
        result2 = screen.check_countdown_cache(10)
        print(f"   相同倒计时(10秒): 应该更新 = {result2}")
        
        print("\n3. 测试倒计时变化")
        result3 = screen.check_countdown_cache(9)
        print(f"   倒计时变化(9秒): 应该更新 = {result3}")
        
        print("\n4. 测试再次相同倒计时")
        result4 = screen.check_countdown_cache(9)
        print(f"   再次相同(9秒): 应该更新 = {result4}")
        
        # 验证结果
        if result1 and not result2 and result3 and not result4:
            print("\n✅ 缓存机制测试通过！")
            print("   - 首次倒计时正确触发更新")
            print("   - 相同倒计时正确跳过更新")
            print("   - 倒计时变化正确触发更新")
            return True
        else:
            print("\n❌ 缓存机制测试失败！")
            print(f"   结果: {result1}, {result2}, {result3}, {result4}")
            print("   期望: True, False, True, False")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("死亡倒计时显示修复测试")
    print("=" * 60)
    
    # 运行所有测试
    test_results = []
    
    # 测试1：死亡倒计时显示
    result1 = test_death_countdown_display()
    test_results.append(("死亡倒计时显示", result1))
    
    # 测试2：倒计时缓存机制
    result2 = test_countdown_cache_mechanism()
    test_results.append(("倒计时缓存机制", result2))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！死亡倒计时显示已修复")
        print("现在死亡倒计时：")
        print("- 只在秒数变化时更新，不会刷屏")
        print("- 在怪物提示区域显示倒计时")
        print("- 在战斗日志中记录倒计时变化")
        print("- 复活后正确清理显示")
    else:
        print("⚠️  部分测试失败，请检查修复情况")
    print("=" * 60)

if __name__ == "__main__":
    main()
