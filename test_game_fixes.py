#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Game.py 修复测试脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_game_imports():
    """测试game.py的导入"""
    print("=== 测试Game.py导入 ===")
    
    try:
        from core.game import Game
        print("✓ Game类导入成功")
        return True
    except Exception as e:
        print(f"✗ Game类导入失败: {e}")
        return False

def test_battle_system_integration():
    """测试战斗系统集成"""
    print("=== 测试战斗系统集成 ===")
    
    try:
        from core.game import Game
        from core.player import Player
        from core.monster import Monster
        
        # 创建游戏实例
        game = Game()
        
        # 检查战斗系统是否存在
        assert hasattr(game, 'battle_system'), "游戏应该有battle_system属性"
        assert game.battle_system is not None, "battle_system不应该为None"
        print("✓ 战斗系统集成检查通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_removed_duplicate_methods():
    """测试重复方法是否已删除"""
    print("=== 测试重复方法删除 ===")
    
    try:
        from core.game import Game
        
        game = Game()
        
        # 检查这些方法是否已被删除或重构
        removed_methods = [
            'player_attack',  # 应该被删除，由BattleSystem处理
            'enemy_attack',   # 应该被删除，由BattleSystem处理
            'check_hit',      # 应该被删除，由DamageCalculator处理
            'calculate_damage' # 应该被删除，由DamageCalculator处理
        ]
        
        for method_name in removed_methods:
            if hasattr(game, method_name):
                print(f"⚠️  方法 {method_name} 仍然存在，可能需要进一步清理")
            else:
                print(f"✓ 方法 {method_name} 已成功删除")
        
        return True
        
    except Exception as e:
        print(f"✗ 重复方法检查失败: {e}")
        return False

def test_update_battle_simplification():
    """测试update_battle方法简化"""
    print("=== 测试update_battle简化 ===")
    
    try:
        from core.game import Game
        import inspect
        
        game = Game()
        
        # 获取update_battle方法的源代码
        source = inspect.getsource(game.update_battle)
        lines = source.split('\n')
        
        # 检查方法是否被简化（行数应该大大减少）
        method_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        
        print(f"update_battle方法有效行数: {method_lines}")
        
        if method_lines < 20:  # 简化后应该少于20行
            print("✓ update_battle方法已成功简化")
        else:
            print("⚠️  update_battle方法可能仍然过于复杂")
        
        # 检查是否委托给battle_system
        if 'battle_system.update' in source:
            print("✓ update_battle正确委托给battle_system")
        else:
            print("⚠️  update_battle可能没有正确委托给battle_system")
        
        return True
        
    except Exception as e:
        print(f"✗ update_battle简化测试失败: {e}")
        return False

def test_code_compilation():
    """测试代码编译"""
    print("=== 测试代码编译 ===")
    
    try:
        import py_compile
        
        # 编译game.py
        py_compile.compile('core/game.py', doraise=True)
        print("✓ core/game.py 编译成功")
        
        # 编译battle.py
        py_compile.compile('core/battle.py', doraise=True)
        print("✓ core/battle.py 编译成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 代码编译失败: {e}")
        return False

def test_battle_delegation():
    """测试战斗委托机制"""
    print("=== 测试战斗委托机制 ===")
    
    try:
        from core.game import Game
        
        game = Game()
        
        # 模拟战斗状态
        game.in_battle = True
        
        # 检查是否有battle_system
        if hasattr(game, 'battle_system') and game.battle_system:
            print("✓ 游戏具有有效的battle_system")
            
            # 检查battle_system是否有update方法
            if hasattr(game.battle_system, 'update'):
                print("✓ battle_system具有update方法")
            else:
                print("✗ battle_system缺少update方法")
                return False
        else:
            print("✗ 游戏缺少battle_system")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗委托测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始Game.py修复测试...\n")
    
    tests = [
        test_game_imports,
        test_code_compilation,
        test_battle_system_integration,
        test_removed_duplicate_methods,
        test_update_battle_simplification,
        test_battle_delegation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 发生异常: {e}")
            print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Game.py修复成功！")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过，修复基本成功")
    else:
        print("❌ 多个测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
