#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战斗系统修复测试脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.battle import BattleSystem, BattleStateManager, DamageCalculator, CooldownManager
from core.player import Player
from core.monster import Monster
from utils.logger import logger

def test_battle_state_manager():
    """测试战斗状态管理器"""
    print("=== 测试战斗状态管理器 ===")
    
    state_manager = BattleStateManager()
    
    # 创建模拟玩家和怪物
    class MockPlayer:
        def __init__(self):
            self.hp = 100
            self.max_hp = 100
    
    class MockMonster:
        def __init__(self):
            self.hp = 50
            self.max_hp = 50
    
    player = MockPlayer()
    monster = MockMonster()
    
    # 测试正常状态
    assert not state_manager.check_battle_end_conditions(player, monster)
    print("✓ 正常状态检查通过")
    
    # 测试玩家死亡
    player.hp = 0
    assert state_manager.check_battle_end_conditions(player, monster)
    assert state_manager.winner == "monster"
    assert state_manager.end_reason == "player_death"
    print("✓ 玩家死亡检查通过")
    
    # 重置状态
    state_manager.reset()
    player.hp = 100
    
    # 测试怪物死亡
    monster.hp = 0
    assert state_manager.check_battle_end_conditions(player, monster)
    assert state_manager.winner == "player"
    assert state_manager.end_reason == "monster_death"
    print("✓ 怪物死亡检查通过")
    
    print("战斗状态管理器测试完成！\n")

def test_damage_calculator():
    """测试伤害计算器"""
    print("=== 测试伤害计算器 ===")
    
    # 测试命中率计算
    hit_rate = DamageCalculator.calculate_hit_rate(100, 50)
    assert 0.3 <= hit_rate <= 0.95
    print(f"✓ 命中率计算: {hit_rate:.2f}")
    
    # 测试边界值
    hit_rate_min = DamageCalculator.calculate_hit_rate(10, 100)
    assert hit_rate_min == 0.3
    print(f"✓ 最小命中率: {hit_rate_min}")
    
    hit_rate_max = DamageCalculator.calculate_hit_rate(200, 10)
    assert hit_rate_max == 0.95
    print(f"✓ 最大命中率: {hit_rate_max}")
    
    print("伤害计算器测试完成！\n")

def test_cooldown_manager():
    """测试冷却管理器"""
    print("=== 测试冷却管理器 ===")
    
    cooldown_manager = CooldownManager()
    current_time = time.time()
    
    # 测试技能冷却
    assert cooldown_manager.can_use_skill("fireball", current_time)
    print("✓ 初始技能可用")
    
    cooldown_manager.set_skill_cooldown("fireball", 2.0, current_time)
    assert not cooldown_manager.can_use_skill("fireball", current_time)
    print("✓ 技能冷却设置成功")
    
    assert cooldown_manager.can_use_skill("fireball", current_time + 3.0)
    print("✓ 技能冷却过期后可用")
    
    # 测试攻击冷却
    assert cooldown_manager.can_attack("player", current_time, 1.0)
    print("✓ 初始攻击可用")
    
    cooldown_manager.set_attack_cooldown("player", current_time)
    assert not cooldown_manager.can_attack("player", current_time, 1.0)
    print("✓ 攻击冷却设置成功")
    
    assert cooldown_manager.can_attack("player", current_time + 1.5, 1.0)
    print("✓ 攻击冷却过期后可用")
    
    print("冷却管理器测试完成！\n")

def test_battle_system_integration():
    """测试战斗系统集成"""
    print("=== 测试战斗系统集成 ===")
    
    try:
        battle_system = BattleSystem()
        
        # 测试初始化
        assert battle_system.state_manager is not None
        assert not battle_system.battle_active
        print("✓ 战斗系统初始化成功")
        
        # 测试状态重置
        battle_system.state_manager.reset()
        assert not battle_system.state_manager.battle_ended
        print("✓ 状态重置成功")
        
        print("战斗系统集成测试完成！\n")
        
    except Exception as e:
        print(f"✗ 战斗系统集成测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始战斗系统修复测试...\n")
    
    try:
        test_battle_state_manager()
        test_damage_calculator()
        test_cooldown_manager()
        
        if test_battle_system_integration():
            print("🎉 所有测试通过！战斗系统修复成功！")
        else:
            print("❌ 部分测试失败，请检查代码")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
