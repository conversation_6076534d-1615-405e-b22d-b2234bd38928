#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查文件引用脚本
查找项目中是否有文件引用了可能需要删除的文件
"""

import os
import re

def check_file_references():
    """检查文件引用"""
    print("=== 检查文件引用 ===")
    
    # 要检查的可能重复文件
    files_to_check = [
        "character_creation_screen",
        "character_creation_screen.py"
    ]
    
    # 搜索的文件扩展名
    extensions = ['.py', '.md', '.txt', '.json']
    
    # 排除的目录
    exclude_dirs = ['__pycache__', '.git', 'node_modules', 'venv', 'env']
    
    references_found = {}
    
    for target_file in files_to_check:
        references_found[target_file] = []
        
        print(f"\n检查对 '{target_file}' 的引用...")
        
        # 遍历项目文件
        for root, dirs, files in os.walk('.'):
            # 排除指定目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                # 只检查指定扩展名的文件
                if any(file.endswith(ext) for ext in extensions):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            
                            # 检查各种引用模式
                            patterns = [
                                rf'import.*{re.escape(target_file)}',
                                rf'from.*{re.escape(target_file)}',
                                rf'"{re.escape(target_file)}"',
                                rf"'{re.escape(target_file)}'",
                                rf'{re.escape(target_file)}\.py',
                            ]
                            
                            for pattern in patterns:
                                if re.search(pattern, content, re.IGNORECASE):
                                    references_found[target_file].append({
                                        'file': file_path,
                                        'pattern': pattern
                                    })
                                    break
                    
                    except Exception as e:
                        # 忽略无法读取的文件
                        pass
    
    # 输出结果
    print("\n=== 检查结果 ===")
    for target_file, references in references_found.items():
        if references:
            print(f"\n❌ '{target_file}' 被以下文件引用:")
            for ref in references:
                print(f"   📄 {ref['file']}")
        else:
            print(f"\n✅ '{target_file}' 没有被其他文件引用，可以安全删除")
    
    return references_found

def check_specific_file_exists():
    """检查特定文件是否存在"""
    print("\n=== 检查文件存在性 ===")
    
    files_to_check = [
        "ui/screens/character_creation_screen.py",
        "ui/screens/character_creation.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} 存在 ({size} 字节)")
        else:
            print(f"❌ {file_path} 不存在")

def analyze_cache_files():
    """分析缓存文件"""
    print("\n=== 分析缓存文件 ===")
    
    cache_files = []
    total_size = 0
    
    for root, dirs, files in os.walk('ui/screens'):
        if '__pycache__' in root:
            for file in files:
                if file.endswith('.pyc') or file.endswith('.pyo'):
                    file_path = os.path.join(root, file)
                    size = os.path.getsize(file_path)
                    cache_files.append((file_path, size))
                    total_size += size
    
    print(f"📊 发现 {len(cache_files)} 个缓存文件")
    print(f"📊 总大小: {total_size / 1024:.2f} KB")
    
    if cache_files:
        print("📄 缓存文件列表:")
        for file_path, size in cache_files[:10]:  # 只显示前10个
            print(f"   {file_path} ({size} 字节)")
        if len(cache_files) > 10:
            print(f"   ... 还有 {len(cache_files) - 10} 个文件")
    
    return cache_files

def generate_cleanup_script():
    """生成清理脚本"""
    print("\n=== 生成清理脚本 ===")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI Screens 自动清理脚本
安全删除缓存文件和确认的重复文件
"""

import os
import shutil
from datetime import datetime

def cleanup_cache_files():
    """清理缓存文件"""
    print("清理Python缓存文件...")
    
    deleted_count = 0
    deleted_size = 0
    
    for root, dirs, files in os.walk("ui/screens"):
        if "__pycache__" in dirs:
            cache_dir = os.path.join(root, "__pycache__")
            try:
                # 计算大小
                for file in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, file)
                    if os.path.isfile(file_path):
                        deleted_size += os.path.getsize(file_path)
                        deleted_count += 1
                
                # 删除目录
                shutil.rmtree(cache_dir)
                print(f"✅ 已删除: {cache_dir}")
            except Exception as e:
                print(f"❌ 删除失败: {cache_dir} - {e}")
    
    print(f"📊 共删除 {deleted_count} 个缓存文件")
    print(f"📊 释放空间: {deleted_size / 1024:.2f} KB")

def main():
    """主函数"""
    print("开始UI Screens清理...")
    print("=" * 50)
    
    # 清理缓存文件
    cleanup_cache_files()
    
    print("=" * 50)
    print("清理完成！")

if __name__ == "__main__":
    main()
'''
    
    with open('cleanup_ui_screens.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 已生成清理脚本: cleanup_ui_screens.py")

def main():
    """主函数"""
    print("开始检查UI Screens文件...")
    
    # 检查文件存在性
    check_specific_file_exists()
    
    # 检查文件引用
    references = check_file_references()
    
    # 分析缓存文件
    cache_files = analyze_cache_files()
    
    # 生成清理脚本
    generate_cleanup_script()
    
    # 总结建议
    print("\n=== 清理建议 ===")
    
    # 缓存文件建议
    if cache_files:
        print(f"🧹 建议立即删除 {len(cache_files)} 个缓存文件 (释放 {sum(size for _, size in cache_files) / 1024:.2f} KB)")
    
    # 重复文件建议
    has_references = any(refs for refs in references.values())
    if not has_references:
        print("🗑️  character_creation_screen.py 可以安全删除（如果存在）")
    else:
        print("⚠️  发现文件引用，删除前需要仔细检查")
    
    print("\n✅ 检查完成！可以运行 cleanup_ui_screens.py 进行清理")

if __name__ == "__main__":
    main()
